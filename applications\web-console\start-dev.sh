#!/bin/bash

# 前端开发启动脚本

set -e

echo "🚀 启动 Athena Web Console 开发环境..."

# 检查 Node.js 和 npm
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo "❌ npm 未安装，请先安装 npm"
    exit 1
fi

# 检查是否已安装依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖..."
    npm install
fi

echo "🔧 检查后端连接..."

# 检查 API Gateway 是否可访问
if curl -s http://localhost:8080/actuator/health > /dev/null; then
    echo "✅ API Gateway 连接正常"
else
    echo "⚠️  API Gateway 不可访问，请确保已运行："
    echo "   kubectl port-forward svc/api-gateway-svc 8080:8080"
    echo ""
    echo "🔄 继续启动前端服务..."
fi

echo "🌐 启动开发服务器..."
echo ""
echo "📋 访问信息："
echo "- 前端地址: http://localhost:5173"
echo "- API 代理: http://localhost:8080"
echo ""
echo "🔑 默认登录信息："
echo "- 用户名: admin"
echo "- 密码: admin123"
echo ""

# 启动开发服务器
npm run dev
