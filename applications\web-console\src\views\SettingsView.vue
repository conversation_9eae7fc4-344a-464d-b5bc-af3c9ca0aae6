<template>
  <div class="settings-container">
    <!-- 页面头部 -->
    <div class="settings-header">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">系统设置</h1>
          <p class="page-description">个性化配置您的工作环境</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" :icon="Download" @click="handleExportSettings">
            导出设置
          </el-button>
          <el-upload
            :before-upload="handleImportSettings"
            :show-file-list="false"
            accept=".json"
          >
            <el-button :icon="Upload">导入设置</el-button>
          </el-upload>
        </div>
      </div>
    </div>

    <!-- 设置内容区域 -->
    <div class="settings-body">
      <!-- 设置导航卡片 -->
      <div class="settings-nav">
        <div class="nav-cards">
          <div
            v-for="tab in settingTabs"
            :key="tab.key"
            class="nav-card"
            :class="{ active: activeTab === tab.key }"
            @click="handleTabChange(tab.key)"
          >
            <div class="nav-card-icon" :style="{ background: tab.color }">
              <el-icon :size="24">
                <component :is="tab.icon" />
              </el-icon>
            </div>
            <div class="nav-card-content">
              <h3 class="nav-card-title">{{ tab.title }}</h3>
              <p class="nav-card-desc">{{ tab.description }}</p>
            </div>
            <div class="nav-card-arrow">
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>
        </div>
      </div>

      <!-- 设置面板 -->
      <div class="settings-panels">
        <!-- 外观设置 -->
        <div v-show="activeTab === 'appearance'" class="setting-panel">
          <el-card class="setting-card">
            <template #header>
              <div class="card-header">
                <div class="card-header-icon">
                  <el-icon><Sunny /></el-icon>
                </div>
                <div>
                  <h3>外观设置</h3>
                  <p>自定义界面主题和视觉效果</p>
                </div>
              </div>
            </template>
            
            <div class="setting-section">
              <h4 class="section-title">主题模式</h4>
              <div class="theme-modes">
                <div
                  v-for="mode in themeModes"
                  :key="mode.value"
                  class="theme-mode-card"
                  :class="{ active: themeStore.mode === mode.value }"
                  @click="themeStore.setMode(mode.value)"
                >
                  <div class="theme-preview" :class="`theme-preview--${mode.value}`">
                    <div class="preview-header"></div>
                    <div class="preview-content">
                      <div class="preview-sidebar"></div>
                      <div class="preview-main"></div>
                    </div>
                  </div>
                  <div class="theme-info">
                    <el-icon class="theme-icon">
                      <component :is="mode.icon" />
                    </el-icon>
                    <span class="theme-name">{{ mode.name }}</span>
                  </div>
                </div>
              </div>
            </div>

            <el-divider />

            <div class="setting-section">
              <h4 class="section-title">配色方案</h4>
              <div class="color-schemes">
                <div
                  v-for="(colors, scheme) in themeStore.colorSchemes"
                  :key="scheme"
                  class="color-scheme-card"
                  :class="{ active: themeStore.colorScheme === scheme }"
                  @click="themeStore.setColorScheme(scheme)"
                >
                  <div class="color-preview">
                    <div class="color-primary" :style="{ background: colors.primary }"></div>
                    <div class="color-secondary" :style="{ background: colors.light }"></div>
                  </div>
                  <span class="scheme-name">{{ getColorSchemeName(scheme) }}</span>
                </div>
              </div>
            </div>

            <el-divider />

            <div class="setting-section">
              <div class="setting-row">
                <div class="setting-info">
                  <h4>界面缩放</h4>
                  <p>调整界面元素的大小</p>
                </div>
                <div class="setting-control">
                  <el-slider
                    v-model="themeStore.scale"
                    :min="80"
                    :max="120"
                    :step="10"
                    :format-tooltip="(val) => `${val}%`"
                    @change="themeStore.setScale"
                    style="width: 200px;"
                  />
                </div>
              </div>

              <div class="setting-row">
                <div class="setting-info">
                  <h4>动画效果</h4>
                  <p>启用界面过渡动画</p>
                </div>
                <div class="setting-control">
                  <el-switch
                    v-model="themeStore.animations"
                    @change="themeStore.setAnimations"
                  />
                </div>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 语言设置 -->
        <div v-show="activeTab === 'language'" class="setting-panel">
          <el-card class="setting-card">
            <template #header>
              <div class="card-header">
                <div class="card-header-icon">
                  <el-icon><Compass /></el-icon>
                </div>
                <div>
                  <h3>语言与地区</h3>
                  <p>设置界面语言和地区格式</p>
                </div>
              </div>
            </template>
            
            <div class="setting-section">
              <div class="setting-row">
                <div class="setting-info">
                  <h4>界面语言</h4>
                  <p>选择系统界面显示语言</p>
                </div>
                <div class="setting-control">
                  <el-select v-model="currentLocale" @change="handleLanguageChange" style="width: 200px;">
                    <el-option
                      v-for="locale in supportedLocales"
                      :key="locale.code"
                      :label="locale.name"
                      :value="locale.code"
                    >
                      <span>{{ locale.flag }} {{ locale.name }}</span>
                    </el-option>
                  </el-select>
                </div>
              </div>

              <div class="setting-row">
                <div class="setting-info">
                  <h4>时区</h4>
                  <p>设置系统时区</p>
                </div>
                <div class="setting-control">
                  <el-select v-model="settingsStore.preferences.timezone" style="width: 200px;">
                    <el-option label="北京时间 (UTC+8)" value="Asia/Shanghai" />
                    <el-option label="东京时间 (UTC+9)" value="Asia/Tokyo" />
                    <el-option label="纽约时间 (UTC-5)" value="America/New_York" />
                    <el-option label="伦敦时间 (UTC+0)" value="Europe/London" />
                  </el-select>
                </div>
              </div>

              <div class="setting-row">
                <div class="setting-info">
                  <h4>日期格式</h4>
                  <p>选择日期显示格式</p>
                </div>
                <div class="setting-control">
                  <el-select v-model="settingsStore.preferences.dateFormat" style="width: 200px;">
                    <el-option label="YYYY-MM-DD" value="YYYY-MM-DD" />
                    <el-option label="MM/DD/YYYY" value="MM/DD/YYYY" />
                    <el-option label="DD/MM/YYYY" value="DD/MM/YYYY" />
                    <el-option label="YYYY年MM月DD日" value="YYYY年MM月DD日" />
                  </el-select>
                </div>
              </div>

              <div class="setting-row">
                <div class="setting-info">
                  <h4>时间格式</h4>
                  <p>选择时间显示格式</p>
                </div>
                <div class="setting-control">
                  <el-radio-group v-model="settingsStore.preferences.timeFormat">
                    <el-radio label="24">24小时制</el-radio>
                    <el-radio label="12">12小时制</el-radio>
                  </el-radio-group>
                </div>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 通知设置 -->
        <div v-show="activeTab === 'notification'" class="setting-panel">
          <el-card class="setting-card">
            <template #header>
              <div class="card-header">
                <div class="card-header-icon">
                  <el-icon><Bell /></el-icon>
                </div>
                <div>
                  <h3>通知设置</h3>
                  <p>管理系统通知和提醒</p>
                </div>
              </div>
            </template>

            <div class="setting-section">
              <div class="setting-row">
                <div class="setting-info">
                  <h4>桌面通知</h4>
                  <p>允许显示桌面通知</p>
                </div>
                <div class="setting-control">
                  <el-switch
                    v-model="notificationStore.enableDesktop"
                    @change="(val) => notificationStore.setDesktopEnabled(val)"
                  />
                </div>
              </div>

              <div class="setting-row">
                <div class="setting-info">
                  <h4>声音提醒</h4>
                  <p>播放通知声音</p>
                </div>
                <div class="setting-control">
                  <el-switch
                    v-model="notificationStore.enableSound"
                    @change="(val) => notificationStore.setSoundEnabled(val)"
                  />
                </div>
              </div>

              <div class="setting-row">
                <div class="setting-info">
                  <h4>邮件通知</h4>
                  <p>发送重要通知到邮箱</p>
                </div>
                <div class="setting-control">
                  <el-switch
                    v-model="emailNotificationEnabled"
                    @change="handleEmailNotificationChange"
                  />
                </div>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 账户设置 -->
        <div v-show="activeTab === 'account'" class="setting-panel">
          <el-card class="setting-card">
            <template #header>
              <div class="card-header">
                <div class="card-header-icon">
                  <el-icon><User /></el-icon>
                </div>
                <div>
                  <h3>账户设置</h3>
                  <p>个人信息和安全设置</p>
                </div>
              </div>
            </template>

            <div class="setting-section">
              <div class="setting-row">
                <div class="setting-info">
                  <h4>用户名</h4>
                  <p>当前登录用户名</p>
                </div>
                <div class="setting-control">
                  <el-input v-model="authStore.username" disabled style="width: 200px;" />
                </div>
              </div>

              <div class="setting-row">
                <div class="setting-info">
                  <h4>修改密码</h4>
                  <p>更改登录密码</p>
                </div>
                <div class="setting-control">
                  <el-button type="primary" @click="handleChangePassword">修改密码</el-button>
                </div>
              </div>

              <div class="setting-row">
                <div class="setting-info">
                  <h4>自动登录</h4>
                  <p>记住登录状态</p>
                </div>
                <div class="setting-control">
                  <el-switch v-model="settingsStore.preferences.autoLogin" />
                </div>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 系统信息 -->
        <div v-show="activeTab === 'system'" class="setting-panel">
          <el-card class="setting-card">
            <template #header>
              <div class="card-header">
                <div class="card-header-icon">
                  <el-icon><Setting /></el-icon>
                </div>
                <div>
                  <h3>系统信息</h3>
                  <p>版本信息和系统状态</p>
                </div>
              </div>
            </template>

            <div class="setting-section">
              <el-descriptions :column="2" border>
                <el-descriptions-item label="系统版本">
                  {{ settingsStore.formattedVersion }}
                </el-descriptions-item>
                <el-descriptions-item label="构建时间">
                  {{ settingsStore.systemSettings.buildTime }}
                </el-descriptions-item>
                <el-descriptions-item label="运行环境">
                  {{ settingsStore.systemSettings.environment }}
                </el-descriptions-item>
                <el-descriptions-item label="API地址">
                  {{ settingsStore.systemSettings.apiUrl }}
                </el-descriptions-item>
              </el-descriptions>

              <el-divider>功能特性</el-divider>
              <div class="feature-list">
                <el-tag
                  v-for="(enabled, feature) in settingsStore.systemSettings.features"
                  :key="feature"
                  :type="enabled ? 'success' : 'info'"
                  class="feature-tag"
                >
                  {{ feature }}: {{ enabled ? '已启用' : '未启用' }}
                </el-tag>
              </div>

              <el-divider>系统状态</el-divider>
              <div class="system-status">
                <StatCard
                  :value="'正常'"
                  label="系统状态"
                  icon="CircleCheck"
                  icon-color="#10b981"
                  :trend="{ value: 99.9, type: 'up', text: '99.9%' }"
                  size="small"
                />
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Sunny,
  Compass,
  Bell,
  User,
  Setting,
  Download,
  Upload,
  RefreshLeft,
  ArrowRight,
  Moon,
  Monitor
} from '@element-plus/icons-vue'

import { useThemeStore } from '@/stores/theme'
import { useNotificationStore } from '@/stores/notification'
import { useSettingsStore } from '@/stores/settings'
import { useAuthStore } from '@/stores/auth'
import { supportedLocales, getCurrentLocale, setLocale } from '@/locales'
import StatCard from '@/components/Charts/StatCard.vue'

// Store实例
const themeStore = useThemeStore()
const notificationStore = useNotificationStore()
const settingsStore = useSettingsStore()
const authStore = useAuthStore()

// 响应式数据
const activeTab = ref('appearance')
const currentLocale = ref(getCurrentLocale())
const emailNotificationEnabled = ref(localStorage.getItem('email-notifications') === 'true')

// 设置标签页配置
const settingTabs = [
  {
    key: 'appearance',
    title: '外观设置',
    description: '主题、颜色和界面样式',
    icon: 'Sunny',
    color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
  },
  {
    key: 'language',
    title: '语言与地区',
    description: '界面语言和地区格式',
    icon: 'Compass',
    color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
  },
  {
    key: 'notification',
    title: '通知设置',
    description: '通知方式和提醒设置',
    icon: 'Bell',
    color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
  },
  {
    key: 'account',
    title: '账户设置',
    description: '个人信息和安全设置',
    icon: 'User',
    color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)'
  },
  {
    key: 'system',
    title: '系统信息',
    description: '版本信息和系统状态',
    icon: 'Setting',
    color: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)'
  }
]

// 主题模式配置
const themeModes = [
  { value: 'light', name: '浅色模式', icon: 'Sunny' },
  { value: 'dark', name: '深色模式', icon: 'Moon' },
  { value: 'auto', name: '跟随系统', icon: 'Monitor' }
]

// 计算属性
const getColorSchemeName = (scheme: string) => {
  const names: Record<string, string> = {
    blue: '蓝色',
    green: '绿色',
    purple: '紫色',
    orange: '橙色',
    red: '红色'
  }
  return names[scheme] || scheme
}

// 方法
const handleTabChange = (key: string) => {
  activeTab.value = key
}

const handleLanguageChange = (locale: string) => {
  setLocale(locale)
  ElMessage.success('语言设置已更新')
}

const handleImportSettings = async (file: File) => {
  try {
    await settingsStore.importSettings(file)
    ElMessage.success('设置导入成功')
  } catch (error: any) {
    ElMessage.error(error.message || '设置导入失败')
  }
  return false // 阻止自动上传
}

const handleExportSettings = async () => {
  try {
    await settingsStore.exportSettings()
    ElMessage.success('设置导出成功')
  } catch (error: any) {
    ElMessage.error(error.message || '设置导出失败')
  }
}

const handleChangePassword = () => {
  ElMessageBox.prompt('请输入新密码', '修改密码', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputType: 'password'
  }).then(({ value }) => {
    // 这里应该调用API修改密码
    ElMessage.success('密码修改成功')
  }).catch(() => {
    ElMessage.info('已取消修改')
  })
}

const handleEmailNotificationChange = (enabled: boolean) => {
  emailNotificationEnabled.value = enabled
  localStorage.setItem('email-notifications', enabled.toString())
  ElMessage.success(enabled ? '邮件通知已启用' : '邮件通知已关闭')
}
</script>

<style scoped>
.settings-container {
  min-height: 100vh;
  background: var(--bg-secondary);
}

.settings-header {
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-light);
  padding: 24px 0;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-info {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.settings-body {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  display: flex;
  gap: 24px;
}

.settings-nav {
  width: 320px;
  flex-shrink: 0;
}

.nav-cards {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.nav-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 16px;
}

.nav-card:hover {
  border-color: var(--primary-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.nav-card.active {
  border-color: var(--primary-color);
  background: var(--primary-light-9);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.nav-card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.nav-card-content {
  flex: 1;
  min-width: 0;
}

.nav-card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 4px 0;
}

.nav-card-desc {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.4;
}

.nav-card-arrow {
  color: var(--text-tertiary);
  transition: all 0.3s ease;
}

.nav-card:hover .nav-card-arrow,
.nav-card.active .nav-card-arrow {
  color: var(--primary-color);
  transform: translateX(4px);
}

.settings-panels {
  flex: 1;
  min-width: 0;
}

.setting-panel {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.setting-card {
  border-radius: 12px;
  border: 1px solid var(--border-light);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 16px;
}

.card-header-icon {
  width: 40px;
  height: 40px;
  background: var(--primary-light-8);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
}

.card-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 4px 0;
}

.card-header p {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0;
}

.setting-section {
  padding: 0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 16px 0;
}

.setting-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid var(--border-lighter);
}

.setting-row:last-child {
  border-bottom: none;
}

.setting-info h4 {
  font-size: 15px;
  font-weight: 500;
  color: var(--text-primary);
  margin: 0 0 4px 0;
}

.setting-info p {
  font-size: 13px;
  color: var(--text-secondary);
  margin: 0;
}

.setting-control {
  flex-shrink: 0;
}

.theme-modes {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-bottom: 8px;
}

.theme-mode-card {
  border: 2px solid var(--border-light);
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.theme-mode-card:hover {
  border-color: var(--primary-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.theme-mode-card.active {
  border-color: var(--primary-color);
  background: var(--primary-light-9);
}

.theme-preview {
  width: 100%;
  height: 60px;
  border-radius: 8px;
  margin-bottom: 12px;
  overflow: hidden;
  border: 1px solid var(--border-lighter);
}

.theme-preview--light {
  background: #ffffff;
}

.theme-preview--dark {
  background: #1a1a1a;
}

.theme-preview--auto {
  background: linear-gradient(45deg, #ffffff 50%, #1a1a1a 50%);
}

.preview-header {
  height: 12px;
  background: var(--border-light);
}

.preview-content {
  display: flex;
  height: 48px;
}

.preview-sidebar {
  width: 20px;
  background: var(--border-lighter);
}

.preview-main {
  flex: 1;
  background: var(--bg-secondary);
}

.theme-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.theme-icon {
  color: var(--text-secondary);
}

.theme-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.color-schemes {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 12px;
}

.color-scheme-card {
  border: 2px solid var(--border-light);
  border-radius: 10px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.color-scheme-card:hover {
  border-color: var(--primary-color);
  transform: translateY(-2px);
}

.color-scheme-card.active {
  border-color: var(--primary-color);
  background: var(--primary-light-9);
}

.color-preview {
  display: flex;
  height: 24px;
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 8px;
}

.color-primary {
  flex: 2;
}

.color-secondary {
  flex: 1;
}

.scheme-name {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-primary);
}

.feature-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.feature-tag {
  margin: 0;
}

.system-status {
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .settings-body {
    flex-direction: column;
    padding: 16px;
  }

  .settings-nav {
    width: 100%;
  }

  .nav-cards {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .theme-modes {
    grid-template-columns: 1fr;
  }

  .color-schemes {
    grid-template-columns: repeat(3, 1fr);
  }

  .setting-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .setting-control {
    width: 100%;
  }
}
</style>
