<template>
  <div class="settings-container">
    <!-- 设置导航 -->
    <div class="settings-nav">
      <el-menu
        :default-active="activeTab"
        mode="vertical"
        @select="handleTabChange"
        class="settings-menu"
      >
        <el-menu-item index="theme">
          <el-icon><Sunny /></el-icon>
          <span>主题设置</span>
        </el-menu-item>
        <el-menu-item index="language">
          <el-icon><Compass /></el-icon>
          <span>语言设置</span>
        </el-menu-item>
        <el-menu-item index="notification">
          <el-icon><Bell /></el-icon>
          <span>通知设置</span>
        </el-menu-item>
        <el-menu-item index="account">
          <el-icon><User /></el-icon>
          <span>账户设置</span>
        </el-menu-item>
        <el-menu-item index="system">
          <el-icon><Setting /></el-icon>
          <span>系统信息</span>
        </el-menu-item>
      </el-menu>
    </div>

    <!-- 设置内容 -->
    <div class="settings-content">
      <!-- 主题设置 -->
      <div v-show="activeTab === 'theme'" class="setting-panel fade-in">
        <el-card>
          <template #header>
            <h3>主题设置</h3>
          </template>
          
          <el-form label-width="120px">
            <el-form-item label="主题模式">
              <el-radio-group v-model="themeStore.mode" @change="themeStore.setMode">
                <el-radio label="light">浅色模式</el-radio>
                <el-radio label="dark">深色模式</el-radio>
                <el-radio label="auto">跟随系统</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="配色方案">
              <div class="color-schemes">
                <div
                  v-for="(colors, scheme) in themeStore.colorSchemes"
                  :key="scheme"
                  class="color-scheme"
                  :class="{ active: themeStore.colorScheme === scheme }"
                  @click="themeStore.setColorScheme(scheme)"
                >
                  <div class="color-preview" :style="{ background: colors.primary }"></div>
                  <span>{{ getColorSchemeName(scheme) }}</span>
                </div>
              </div>
            </el-form-item>
            
            <el-form-item label="字体大小">
              <el-slider
                v-model="themeStore.fontSize"
                :min="12"
                :max="18"
                :step="1"
                show-stops
                @change="themeStore.setFontSize"
              />
              <span class="font-size-text">{{ themeStore.fontSize }}px</span>
            </el-form-item>
            
            <el-form-item label="紧凑模式">
              <el-switch
                v-model="themeStore.compactMode"
                @change="themeStore.toggleCompactMode"
              />
            </el-form-item>
          </el-form>
        </el-card>
      </div>

      <!-- 语言设置 -->
      <div v-show="activeTab === 'language'" class="setting-panel fade-in">
        <el-card>
          <template #header>
            <h3>语言设置</h3>
          </template>
          
          <el-form label-width="120px">
            <el-form-item label="界面语言">
              <el-select v-model="currentLocale" @change="handleLanguageChange">
                <el-option
                  v-for="locale in supportedLocales"
                  :key="locale.code"
                  :label="locale.name"
                  :value="locale.code"
                >
                  <span>{{ locale.flag }} {{ locale.name }}</span>
                </el-option>
              </el-select>
            </el-form-item>
            
            <el-form-item label="时区">
              <el-select v-model="settingsStore.preferences.timezone">
                <el-option label="北京时间 (UTC+8)" value="Asia/Shanghai" />
                <el-option label="东京时间 (UTC+9)" value="Asia/Tokyo" />
                <el-option label="纽约时间 (UTC-5)" value="America/New_York" />
                <el-option label="伦敦时间 (UTC+0)" value="Europe/London" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="日期格式">
              <el-radio-group v-model="settingsStore.preferences.dateFormat">
                <el-radio label="YYYY-MM-DD">2025-01-18</el-radio>
                <el-radio label="MM/DD/YYYY">01/18/2025</el-radio>
                <el-radio label="DD/MM/YYYY">18/01/2025</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="时间格式">
              <el-radio-group v-model="settingsStore.preferences.timeFormat">
                <el-radio label="24h">24小时制</el-radio>
                <el-radio label="12h">12小时制</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </el-card>
      </div>

      <!-- 通知设置 -->
      <div v-show="activeTab === 'notification'" class="setting-panel fade-in">
        <el-card>
          <template #header>
            <h3>通知设置</h3>
          </template>
          
          <el-form label-width="120px">
            <el-form-item label="声音提醒">
              <el-switch
                v-model="notificationStore.enableSound"
                @change="notificationStore.setSoundEnabled"
              />
            </el-form-item>
            
            <el-form-item label="桌面通知">
              <el-switch
                v-model="notificationStore.enableDesktop"
                @change="notificationStore.setDesktopEnabled"
              />
            </el-form-item>
            
            <el-form-item label="显示提示">
              <el-switch v-model="settingsStore.preferences.showTips" />
            </el-form-item>
            
            <el-form-item label="动画效果">
              <el-switch v-model="settingsStore.preferences.animationsEnabled" />
            </el-form-item>
            
            <el-form-item label="快捷键">
              <el-switch v-model="settingsStore.preferences.keyboardShortcuts" />
            </el-form-item>
          </el-form>
          
          <!-- 通知历史 -->
          <el-divider>通知历史</el-divider>
          <div class="notification-history">
            <div class="history-header">
              <span>未读通知: {{ notificationStore.unreadCount }}</span>
              <el-button size="small" @click="notificationStore.markAllAsRead">
                全部已读
              </el-button>
            </div>
            <div class="notification-list">
              <div
                v-for="notification in notificationStore.recentNotifications"
                :key="notification.id"
                class="notification-item"
                :class="{ unread: !notification.read }"
              >
                <div class="notification-content">
                  <div class="notification-title">{{ notification.title }}</div>
                  <div class="notification-message">{{ notification.message }}</div>
                  <div class="notification-time">{{ formatRelativeTime(notification.timestamp) }}</div>
                </div>
                <el-button
                  size="small"
                  text
                  @click="notificationStore.removeNotification(notification.id)"
                >
                  删除
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 账户设置 -->
      <div v-show="activeTab === 'account'" class="setting-panel fade-in">
        <el-card>
          <template #header>
            <h3>账户设置</h3>
          </template>
          
          <el-form label-width="120px">
            <el-form-item label="用户名">
              <el-input v-model="authStore.username" disabled />
            </el-form-item>
            
            <el-form-item label="自动保存">
              <el-switch v-model="settingsStore.preferences.autoSave" />
            </el-form-item>
            
            <el-form-item label="每页显示">
              <el-input-number
                v-model="settingsStore.preferences.pageSize"
                :min="10"
                :max="100"
                :step="10"
              />
            </el-form-item>
          </el-form>
          
          <el-divider>数据管理</el-divider>
          <div class="data-management">
            <el-button @click="settingsStore.exportSettings">
              <el-icon><Download /></el-icon>
              导出设置
            </el-button>
            <el-upload
              :show-file-list="false"
              :before-upload="handleImportSettings"
              accept=".json"
            >
              <el-button>
                <el-icon><Upload /></el-icon>
                导入设置
              </el-button>
            </el-upload>
            <el-button type="danger" @click="handleResetSettings">
              <el-icon><RefreshLeft /></el-icon>
              重置设置
            </el-button>
          </div>
        </el-card>
      </div>

      <!-- 系统信息 -->
      <div v-show="activeTab === 'system'" class="setting-panel fade-in">
        <el-card>
          <template #header>
            <h3>系统信息</h3>
          </template>
          
          <el-descriptions :column="2" border>
            <el-descriptions-item label="系统版本">
              {{ settingsStore.formattedVersion }}
            </el-descriptions-item>
            <el-descriptions-item label="构建时间">
              {{ settingsStore.systemSettings.buildTime }}
            </el-descriptions-item>
            <el-descriptions-item label="运行环境">
              {{ settingsStore.systemSettings.environment }}
            </el-descriptions-item>
            <el-descriptions-item label="API地址">
              {{ settingsStore.systemSettings.apiUrl }}
            </el-descriptions-item>
          </el-descriptions>
          
          <el-divider>功能特性</el-divider>
          <div class="feature-list">
            <el-tag
              v-for="(enabled, feature) in settingsStore.systemSettings.features"
              :key="feature"
              :type="enabled ? 'success' : 'info'"
              class="feature-tag"
            >
              {{ feature }}: {{ enabled ? '已启用' : '未启用' }}
            </el-tag>
          </div>
          
          <el-divider>系统状态</el-divider>
          <div class="system-status">
            <StatCard
              :value="'正常'"
              label="系统状态"
              icon="CircleCheck"
              icon-color="#10b981"
              :trend="{ value: 99.9, type: 'up', text: '99.9%' }"
              size="small"
            />
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Sunny,
  Compass,
  Bell,
  User,
  Setting,
  Download,
  Upload,
  RefreshLeft
} from '@element-plus/icons-vue'

import { useThemeStore } from '@/stores/theme'
import { useNotificationStore } from '@/stores/notification'
import { useSettingsStore } from '@/stores/settings'
import { useAuthStore } from '@/stores/auth'
import { supportedLocales, getCurrentLocale, setLocale, formatRelativeTime } from '@/locales'
import StatCard from '@/components/Charts/StatCard.vue'

// Store实例
const themeStore = useThemeStore()
const notificationStore = useNotificationStore()
const settingsStore = useSettingsStore()
const authStore = useAuthStore()

// 响应式数据
const activeTab = ref('theme')
const currentLocale = ref(getCurrentLocale())

// 计算属性
const getColorSchemeName = (scheme: string) => {
  const names: Record<string, string> = {
    blue: '蓝色',
    green: '绿色',
    purple: '紫色',
    orange: '橙色',
    red: '红色'
  }
  return names[scheme] || scheme
}

// 方法
const handleTabChange = (key: string) => {
  activeTab.value = key
}

const handleLanguageChange = (locale: string) => {
  setLocale(locale)
  ElMessage.success('语言设置已更新')
}

const handleImportSettings = async (file: File) => {
  try {
    await settingsStore.importSettings(file)
    ElMessage.success('设置导入成功')
  } catch (error: any) {
    ElMessage.error(error.message || '设置导入失败')
  }
  return false // 阻止自动上传
}

const handleResetSettings = async () => {
  try {
    await ElMessageBox.confirm(
      '确认重置所有设置？此操作不可恢复！',
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    settingsStore.resetPreferences()
    themeStore.resetTheme()
    ElMessage.success('设置已重置')
  } catch {
    // 用户取消
  }
}
</script>

<style scoped>
.settings-container {
  display: flex;
  gap: 24px;
  height: calc(100vh - 120px);
}

.settings-nav {
  width: 200px;
  flex-shrink: 0;
}

.settings-menu {
  border-right: 1px solid var(--border-light);
  height: 100%;
}

.settings-content {
  flex: 1;
  overflow-y: auto;
}

.setting-panel {
  max-width: 800px;
}

.color-schemes {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.color-scheme {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border: 2px solid var(--border-light);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
}

.color-scheme:hover {
  border-color: var(--primary-color);
}

.color-scheme.active {
  border-color: var(--primary-color);
  background: var(--primary-color-light-9);
}

.color-preview {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: var(--shadow-sm);
}

.font-size-text {
  margin-left: 16px;
  color: var(--text-secondary);
}

.notification-history {
  margin-top: 16px;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-light);
}

.notification-list {
  max-height: 300px;
  overflow-y: auto;
}

.notification-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px;
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  margin-bottom: 8px;
  transition: all 0.2s ease;
}

.notification-item:hover {
  background: var(--bg-tertiary);
}

.notification-item.unread {
  border-left: 4px solid var(--primary-color);
  background: var(--primary-color-light-9);
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.notification-message {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 4px;
}

.notification-time {
  font-size: 12px;
  color: var(--text-tertiary);
}

.data-management {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.feature-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.feature-tag {
  margin-bottom: 8px;
}

.system-status {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .settings-container {
    flex-direction: column;
    height: auto;
  }

  .settings-nav {
    width: 100%;
  }

  .settings-menu {
    border-right: none;
    border-bottom: 1px solid var(--border-light);
  }

  .color-schemes {
    justify-content: center;
  }

  .data-management {
    justify-content: center;
  }
}

@media (max-width: 576px) {
  .settings-container {
    gap: 16px;
  }

  .notification-item {
    flex-direction: column;
    gap: 8px;
  }

  .system-status {
    grid-template-columns: 1fr;
  }
}
</style>
