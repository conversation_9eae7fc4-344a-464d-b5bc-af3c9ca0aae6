#!/bin/bash

# 构建和部署 admin-service 脚本

set -e

echo "🚀 开始构建 admin-service..."

# 构建 Docker 镜像
echo "📦 构建 Docker 镜像..."
docker build -t athena/admin-service:latest .

echo "🔧 应用 Kubernetes 配置..."

# 应用 RBAC 配置
echo "👤 创建 ServiceAccount 和权限..."
kubectl apply -f k8s/rbac.yaml

# 应用 Deployment 和 Service
echo "🚀 部署服务..."
kubectl apply -f k8s/deployment.yaml

# 等待部署完成
echo "⏳ 等待部署完成..."
kubectl rollout status deployment/admin-service --timeout=300s

# 检查服务状态
echo "✅ 检查服务状态..."
kubectl get pods -l app=admin-service
kubectl get svc admin-service-svc

echo "🎉 admin-service 部署完成！"

# 显示服务信息
echo ""
echo "📋 服务信息:"
echo "- Service: admin-service-svc"
echo "- Port: 80"
echo "- Health Check: http://admin-service-svc/health"
echo ""
echo "🔍 查看日志: kubectl logs -l app=admin-service -f"
