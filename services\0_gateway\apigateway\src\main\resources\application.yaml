server:
  port: 8080

spring:
  application:
    name: api-gateway
  cloud:
    gateway:
      routes:
        - id: user_service_route
          uri: http://user-service-svc:8080
          predicates:
            - Path=/auth/**
        - id: task_service_route
          uri: http://task-service-svc:80
          predicates:
            - Path=/api/tasks/**
          filters:
            - StripPrefix=1
        - id: admin_service_route
          uri: http://admin-service-svc:80
          predicates:
            - Path=/api/admin/**
    kubernetes:
      discovery:
        enabled: true