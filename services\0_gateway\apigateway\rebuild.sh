#!/bin/bash

# 重新构建 API Gateway 脚本

set -e

# 版本号
VERSION="0.0.4"
IMAGE_NAME="athena/api-gateway"
IMAGE_TAG="${IMAGE_NAME}:${VERSION}"
TAR_FILE="api-gateway-${VERSION}.tar"

echo "🚀 重新构建 API Gateway v${VERSION}..."

# 构建 Docker 镜像
echo "📦 构建 Docker 镜像..."
docker buildx build --platform linux/amd64 -t ${IMAGE_TAG} . --load

# 保存镜像为 tar 文件
echo "💾 保存镜像到 tar 文件..."
docker save ${IMAGE_TAG} -o ${TAR_FILE}

# 复制镜像到 minikube
echo "📤 复制镜像到 minikube..."
minikube cp ${TAR_FILE} /home/<USER>/

# 在 minikube 中加载镜像
echo "📥 在 minikube 中加载镜像..."
minikube ssh "docker load -i /home/<USER>/${TAR_FILE}"

# 清理本地 tar 文件
echo "🧹 清理本地 tar 文件..."
rm -f ${TAR_FILE}

# 更新 deployment.yaml 中的镜像版本
echo "📝 更新 deployment.yaml..."
sed -i.bak "s|image: ${IMAGE_NAME}:.*|image: ${IMAGE_TAG}|" k8s/deployment.yaml
rm -f k8s/deployment.yaml.bak

echo "🔄 重新部署服务..."
kubectl apply -f k8s/deployment.yaml

# 等待部署完成
echo "⏳ 等待部署完成..."
kubectl rollout status deployment/api-gateway --timeout=300s

echo "✅ 检查服务状态..."
kubectl get pods -l app=api-gateway
kubectl get svc -l app=api-gateway

echo "🎉 API Gateway v${VERSION} 重新部署完成！"

echo ""
echo "🧪 测试命令:"
echo "kubectl port-forward svc/api-gateway-svc 8080:8080"
echo "curl http://localhost:8080/api/admin/health"
