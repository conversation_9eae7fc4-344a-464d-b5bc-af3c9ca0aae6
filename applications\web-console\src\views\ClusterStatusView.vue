<template>
  <div class="cluster-status-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">集群状态监控</h1>
          <p class="page-description">实时监控 Kubernetes 集群中的 Pod 状态和资源使用情况</p>
        </div>
        <div class="header-actions">
          <el-button 
            type="primary" 
            :icon="Refresh" 
            @click="refreshPods"
            :loading="loading"
          >
            刷新
          </el-button>
          <el-switch
            v-model="autoRefresh"
            @change="toggleAutoRefresh"
            active-text="自动刷新"
            inactive-text="手动刷新"
          />
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="16">
        <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon running">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ podStats.running }}</div>
                <div class="stat-label">运行中</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon pending">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ podStats.pending }}</div>
                <div class="stat-label">等待中</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon failed">
                <el-icon><CircleClose /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ podStats.failed }}</div>
                <div class="stat-label">失败</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon total">
                <el-icon><DataBoard /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ podStats.total }}</div>
                <div class="stat-label">总计</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- Pod 列表表格 -->
    <div class="pods-table">
      <el-card>
        <template #header>
          <div class="card-header">
            <h3>Pod 列表</h3>
            <div class="table-controls">
              <el-input
                v-model="searchText"
                placeholder="搜索 Pod 名称或命名空间"
                :prefix-icon="Search"
                style="width: 300px; margin-right: 16px;"
                clearable
              />
              <el-select
                v-model="selectedNamespace"
                placeholder="选择命名空间"
                style="width: 200px;"
                clearable
                @change="refreshPods"
              >
                <el-option label="所有命名空间" value="" />
                <el-option
                  v-for="ns in namespaces"
                  :key="ns"
                  :label="ns"
                  :value="ns"
                />
              </el-select>
            </div>
          </div>
        </template>

        <el-table
          :data="filteredPods"
          :loading="loading"
          stripe
          style="width: 100%"
          @sort-change="handleSortChange"
        >
          <el-table-column prop="name" label="名称" min-width="200" sortable>
            <template #default="{ row }">
              <div class="pod-name">
                <el-icon class="pod-icon"><Monitor /></el-icon>
                <span>{{ row.name }}</span>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="namespace" label="命名空间" width="150" sortable />
          
          <el-table-column prop="status" label="状态" width="120" sortable>
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)" size="small">
                {{ row.status }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="restart_count" label="重启次数" width="100" sortable />
          
          <el-table-column prop="age" label="运行时间" width="120" sortable />
          
          <el-table-column prop="node_name" label="节点" width="150" sortable />
          
          <el-table-column label="操作" width="300" fixed="right">
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                :icon="View"
                @click="showPodLogs(row)"
              >
                查看日志
              </el-button>
              <el-button
                type="warning"
                size="small"
                :icon="RefreshRight"
                @click="restartPod(row)"
                :disabled="!getDeploymentName(row)"
              >
                重启
              </el-button>
              <el-button
                type="info"
                size="small"
                :icon="Operation"
                @click="showScaleDialog(row)"
                :disabled="!getDeploymentName(row)"
              >
                扩缩容
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 日志查看模态框 -->
    <el-dialog
      v-model="logDialogVisible"
      :title="`Pod 日志 - ${currentPod?.name}`"
      width="80%"
      :before-close="closeLogDialog"
      destroy-on-close
    >
      <div class="log-container">
        <div class="log-controls">
          <el-select
            v-if="currentPod && currentPod.containers.length > 1"
            v-model="selectedContainer"
            placeholder="选择容器"
            style="width: 200px; margin-right: 16px;"
            @change="switchContainer"
          >
            <el-option
              v-for="container in currentPod.containers"
              :key="container.name"
              :label="container.name"
              :value="container.name"
            />
          </el-select>
          <el-button
            type="primary"
            size="small"
            :icon="Download"
            @click="downloadLogs"
          >
            下载日志
          </el-button>
          <el-button
            type="default"
            size="small"
            :icon="RefreshRight"
            @click="clearLogs"
          >
            清空
          </el-button>
        </div>
        <div class="log-content" ref="logContentRef">
          <pre><code>{{ logContent }}</code></pre>
        </div>
      </div>
    </el-dialog>

    <!-- 扩缩容对话框 -->
    <el-dialog
      v-model="scaleDialogVisible"
      title="扩缩容设置"
      width="400px"
    >
      <el-form :model="scaleForm" label-width="100px">
        <el-form-item label="当前副本数">
          <el-input :value="currentReplicas" disabled />
        </el-form-item>
        <el-form-item label="目标副本数" required>
          <el-input-number
            v-model="scaleForm.replicas"
            :min="0"
            :max="10"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="scaleDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmScale" :loading="scaleLoading">
          确认
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh,
  Search,
  View,
  RefreshRight,
  Operation,
  Download,
  CircleCheck,
  CircleClose,
  Clock,
  DataBoard,
  Monitor
} from '@element-plus/icons-vue'
import AdminAPI, { type Pod } from '@/api/admin'

// 响应式数据
const loading = ref(false)
const autoRefresh = ref(true)
const refreshTimer = ref<NodeJS.Timeout | null>(null)
const pods = ref<Pod[]>([])
const searchText = ref('')
const selectedNamespace = ref('')

// 日志相关
const logDialogVisible = ref(false)
const currentPod = ref<Pod | null>(null)
const selectedContainer = ref('')
const logContent = ref('')
const logContentRef = ref<HTMLElement>()
const logStreamReader = ref<{ cancel: () => void } | null>(null)

// 扩缩容相关
const scaleDialogVisible = ref(false)
const scaleLoading = ref(false)
const currentReplicas = ref(0)
const scaleForm = reactive({
  replicas: 1
})

// 计算属性
const namespaces = computed(() => {
  const nsSet = new Set(pods.value.map(pod => pod.namespace))
  return Array.from(nsSet).sort()
})

const filteredPods = computed(() => {
  let filtered = pods.value

  // 命名空间过滤
  if (selectedNamespace.value) {
    filtered = filtered.filter(pod => pod.namespace === selectedNamespace.value)
  }

  // 搜索过滤
  if (searchText.value) {
    const search = searchText.value.toLowerCase()
    filtered = filtered.filter(pod =>
      pod.name.toLowerCase().includes(search) ||
      pod.namespace.toLowerCase().includes(search)
    )
  }

  return filtered
})

const podStats = computed(() => {
  const stats = {
    total: filteredPods.value.length,
    running: 0,
    pending: 0,
    failed: 0
  }

  filteredPods.value.forEach(pod => {
    switch (pod.status.toLowerCase()) {
      case 'running':
        stats.running++
        break
      case 'pending':
        stats.pending++
        break
      case 'failed':
      case 'error':
        stats.failed++
        break
    }
  })

  return stats
})

// 方法
const refreshPods = async () => {
  loading.value = true
  try {
    pods.value = await AdminAPI.getPods(selectedNamespace.value || undefined)
  } catch (error) {
    ElMessage.error('获取 Pod 列表失败')
    console.error('Failed to fetch pods:', error)
  } finally {
    loading.value = false
  }
}

const toggleAutoRefresh = (enabled: boolean) => {
  if (enabled) {
    refreshTimer.value = setInterval(refreshPods, 10000) // 每10秒刷新
  } else {
    if (refreshTimer.value) {
      clearInterval(refreshTimer.value)
      refreshTimer.value = null
    }
  }
}

const getStatusTagType = (status: string) => {
  switch (status.toLowerCase()) {
    case 'running':
      return 'success'
    case 'pending':
      return 'warning'
    case 'failed':
    case 'error':
      return 'danger'
    default:
      return 'info'
  }
}

const getDeploymentName = (pod: Pod): string | null => {
  // 尝试从 Pod 名称推断 Deployment 名称
  // 通常 Pod 名称格式为: deployment-name-xxx-xxx
  const name = pod.name
  const parts = name.split('-')
  if (parts.length >= 3) {
    // 移除最后两个部分（通常是随机字符串）
    return parts.slice(0, -2).join('-')
  }
  return null
}

const showPodLogs = async (pod: Pod) => {
  currentPod.value = pod
  selectedContainer.value = pod.containers[0]?.name || ''
  logContent.value = ''
  logDialogVisible.value = true

  await nextTick()
  startLogStream()
}

const startLogStream = async () => {
  if (!currentPod.value) return

  try {
    const stream = await AdminAPI.getPodLogs(
      currentPod.value.namespace,
      currentPod.value.name,
      {
        container: selectedContainer.value,
        follow: true,
        tailLines: 100
      }
    )

    logStreamReader.value = AdminAPI.createLogStreamReader(
      stream,
      (chunk) => {
        logContent.value += chunk
        // 自动滚动到底部
        nextTick(() => {
          if (logContentRef.value) {
            logContentRef.value.scrollTop = logContentRef.value.scrollHeight
          }
        })
      },
      (error) => {
        ElMessage.error('日志流读取失败')
        console.error('Log stream error:', error)
      }
    )
  } catch (error) {
    ElMessage.error('获取日志失败')
    console.error('Failed to get logs:', error)
  }
}

const switchContainer = () => {
  logContent.value = ''
  if (logStreamReader.value) {
    logStreamReader.value.cancel()
  }
  startLogStream()
}

const closeLogDialog = () => {
  if (logStreamReader.value) {
    logStreamReader.value.cancel()
    logStreamReader.value = null
  }
  logDialogVisible.value = false
  currentPod.value = null
  logContent.value = ''
}

const clearLogs = () => {
  logContent.value = ''
}

const downloadLogs = () => {
  if (!logContent.value) {
    ElMessage.warning('没有日志内容可下载')
    return
  }

  const blob = new Blob([logContent.value], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `${currentPod.value?.name}-logs.txt`
  a.click()
  URL.revokeObjectURL(url)
}

const restartPod = async (pod: Pod) => {
  const deploymentName = getDeploymentName(pod)
  if (!deploymentName) {
    ElMessage.warning('无法确定 Deployment 名称')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要重启 Deployment "${deploymentName}" 吗？这将重启所有相关的 Pod。`,
      '确认重启',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await AdminAPI.restartDeployment(pod.namespace, deploymentName)
    ElMessage.success('重启命令已发送')
    refreshPods()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('重启失败')
      console.error('Failed to restart deployment:', error)
    }
  }
}

const showScaleDialog = (pod: Pod) => {
  const deploymentName = getDeploymentName(pod)
  if (!deploymentName) {
    ElMessage.warning('无法确定 Deployment 名称')
    return
  }

  // 计算当前副本数（同一个 deployment 的 pod 数量）
  currentReplicas.value = pods.value.filter(p => 
    p.namespace === pod.namespace && getDeploymentName(p) === deploymentName
  ).length

  scaleForm.replicas = currentReplicas.value
  scaleDialogVisible.value = true
}

const confirmScale = async () => {
  if (!currentPod.value) return

  const deploymentName = getDeploymentName(currentPod.value)
  if (!deploymentName) {
    ElMessage.warning('无法确定 Deployment 名称')
    return
  }

  scaleLoading.value = true
  try {
    await AdminAPI.scaleDeployment(
      currentPod.value.namespace,
      deploymentName,
      scaleForm.replicas
    )
    ElMessage.success('扩缩容命令已发送')
    scaleDialogVisible.value = false
    refreshPods()
  } catch (error) {
    ElMessage.error('扩缩容失败')
    console.error('Failed to scale deployment:', error)
  } finally {
    scaleLoading.value = false
  }
}

const handleSortChange = ({ prop, order }: any) => {
  // 这里可以实现排序逻辑
  console.log('Sort change:', prop, order)
}

// 生命周期
onMounted(() => {
  refreshPods()
  toggleAutoRefresh(autoRefresh.value)
})

onUnmounted(() => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
  }
  if (logStreamReader.value) {
    logStreamReader.value.cancel()
  }
})
</script>

<style scoped>
.cluster-status-container {
  min-height: 100vh;
  background: var(--bg-secondary);
  padding: 0;
}

.page-header {
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-light);
  padding: 24px 0;
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-info {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stats-cards {
  max-width: 1400px;
  margin: 24px auto;
  padding: 0 24px;
}

.stat-card {
  border-radius: 12px;
  border: 1px solid var(--border-light);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.stat-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

.stat-icon.running {
  background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
}

.stat-icon.pending {
  background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
}

.stat-icon.failed {
  background: linear-gradient(135deg, #ef4444 0%, #f87171 100%);
}

.stat-icon.total {
  background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
}

.pods-table {
  max-width: 1400px;
  margin: 0 auto 24px;
  padding: 0 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.table-controls {
  display: flex;
  align-items: center;
}

.pod-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pod-icon {
  color: var(--primary-color);
}

.log-container {
  height: 500px;
  display: flex;
  flex-direction: column;
}

.log-controls {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-light);
}

.log-content {
  flex: 1;
  background: #1e1e1e;
  border-radius: 8px;
  padding: 16px;
  overflow-y: auto;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

.log-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.log-content code {
  color: #d4d4d4;
  font-size: 13px;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: space-between;
  }

  .table-controls {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .table-controls .el-input,
  .table-controls .el-select {
    width: 100% !important;
  }

  .log-controls {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  font-weight: 600;
}

:deep(.el-table td) {
  border-bottom: 1px solid var(--border-lighter);
}

:deep(.el-table tr:hover > td) {
  background-color: var(--primary-light-9);
}

/* 对话框样式 */
:deep(.el-dialog) {
  border-radius: 12px;
}

:deep(.el-dialog__header) {
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-light);
  border-radius: 12px 12px 0 0;
}

:deep(.el-dialog__body) {
  padding: 24px;
}
</style>
